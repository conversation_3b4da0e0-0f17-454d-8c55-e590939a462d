import axios from 'axios';

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
const BASE_URL = 'https://maps.googleapis.com/maps/api/place';

export const googlePlacesApi = {
  async getNearbyRestaurants({ lat, lng, radius }) {
    try {
      const response = await axios.get(`${BASE_URL}/nearbysearch/json`, {
        params: {
          location: `${lat},${lng}`,
          radius,
          type: 'restaurant',
          key: GOOGLE_API_KEY,
        },
      });
      
      console.log(response.data);

      // Transform response for frontend
      const restaurants = response.data.results.map((place) => ({
        id: place.place_id,
        name: place.name,
        address: place.vicinity,
        rating: place.rating || 'N/A',
        photo: place.photos
          ? `https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=${place.photos[0].photo_reference}&key=${GOOGLE_API_KEY}`
          : null,
      }));

      return restaurants;
    } catch (error) {
      console.error('Google Places API error:', error);
      throw new Error('Failed to fetch nearby restaurants');
    }
  },

  async getPlaceDetails(placeId) {
    try {
      const response = await axios.get(`${BASE_URL}/details/json`, {
        params: {
          place_id: placeId,
          key: GOOGLE_API_KEY,
        },
      });
      return response.data.result;
    } catch (error) {
      console.error('Google Place Details error:', error);
      throw new Error('Failed to fetch place details');
    }
  },
};