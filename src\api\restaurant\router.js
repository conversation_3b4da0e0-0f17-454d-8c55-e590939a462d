import { googlePlacesApi } from '../google/google.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    console.log(searchParams);
    const lat = searchParams.get('lat') || '37.7749'; // Default: San Francisco
    const lng = searchParams.get('lng') || '-122.4194';
    const radius = searchParams.get('radius') || '5000'; // 5km radius
    console.log(lat, lng, radius);

    // Fetch nearby restaurants
    const restaurants = await googlePlacesApi.getNearbyRestaurants({ lat, lng, radius });

    return new Response(JSON.stringify({ restaurants }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error fetching restaurants:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch restaurants', details: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}